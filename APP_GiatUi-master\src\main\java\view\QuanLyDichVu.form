<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.5" maxVersion="1.9" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <NonVisualComponents>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="jTable1">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="4" rowCount="4">
                <Column editable="true" title="Title 1" type="java.lang.Object"/>
                <Column editable="true" title="Title 2" type="java.lang.Object"/>
                <Column editable="true" title="Title 3" type="java.lang.Object"/>
                <Column editable="true" title="Title 4" type="java.lang.Object"/>
              </Table>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
  </NonVisualComponents>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Component id="jPanel1" alignment="0" max="32767" attributes="0"/>
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace min="-2" pref="48" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="103" groupAlignment="0" max="-2" attributes="0">
                      <Component id="jScrollPane2" pref="737" max="32767" attributes="0"/>
                      <Group type="102" alignment="0" attributes="0">
                          <Group type="103" groupAlignment="0" attributes="0">
                              <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel2" alignment="0" min="-2" max="-2" attributes="0"/>
                              <Component id="jLabel4" alignment="0" min="-2" pref="133" max="-2" attributes="0"/>
                          </Group>
                          <EmptySpace min="-2" pref="40" max="-2" attributes="0"/>
                          <Group type="103" groupAlignment="0" max="-2" attributes="0">
                              <Group type="102" attributes="0">
                                  <Component id="btnthem" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace max="32767" attributes="0"/>
                                  <Component id="btnsua" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace min="-2" pref="68" max="-2" attributes="0"/>
                                  <Component id="btnxoa" min="-2" max="-2" attributes="0"/>
                                  <EmptySpace min="-2" pref="58" max="-2" attributes="0"/>
                                  <Component id="btnlammoi" min="-2" max="-2" attributes="0"/>
                              </Group>
                              <Component id="txtmadichvu" pref="486" max="32767" attributes="0"/>
                              <Component id="txtdongia" pref="486" max="32767" attributes="0"/>
                              <Component id="cbodichvu" alignment="0" max="32767" attributes="0"/>
                          </Group>
                          <EmptySpace min="-2" pref="29" max="-2" attributes="0"/>
                      </Group>
                  </Group>
                  <Component id="jLabel6" alignment="0" min="-2" pref="171" max="-2" attributes="0"/>
              </Group>
              <EmptySpace pref="50" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <Component id="jPanel1" min="-2" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="43" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="txtmadichvu" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                  <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="35" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="cbodichvu" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
              </Group>
              <EmptySpace min="-2" pref="31" max="-2" attributes="0"/>
              <Group type="103" groupAlignment="1" attributes="0">
                  <Component id="jLabel5" min="-2" max="-2" attributes="0"/>
                  <Component id="txtdongia" min="-2" pref="30" max="-2" attributes="0"/>
              </Group>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" pref="58" max="-2" attributes="0"/>
                      <Component id="jLabel4" min="-2" max="-2" attributes="0"/>
                  </Group>
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace min="-2" pref="36" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="btnthem" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                          <Component id="btnlammoi" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                          <Component id="btnxoa" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                          <Component id="btnsua" alignment="3" min="-2" pref="30" max="-2" attributes="0"/>
                      </Group>
                  </Group>
              </Group>
              <EmptySpace type="separate" max="-2" attributes="0"/>
              <Component id="jScrollPane2" min="-2" pref="176" max="-2" attributes="0"/>
              <EmptySpace min="-2" pref="31" max="-2" attributes="0"/>
              <Component id="jLabel6" min="-2" max="-2" attributes="0"/>
              <EmptySpace pref="111" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="jPanel1">

      <Layout>
        <DimensionLayout dim="0">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace min="-2" pref="274" max="-2" attributes="0"/>
                  <Component id="jLabel1" min="-2" pref="192" max="-2" attributes="0"/>
                  <EmptySpace max="32767" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
        <DimensionLayout dim="1">
          <Group type="103" groupAlignment="0" attributes="0">
              <Group type="102" alignment="0" attributes="0">
                  <EmptySpace max="-2" attributes="0"/>
                  <Component id="jLabel1" max="32767" attributes="0"/>
                  <EmptySpace max="-2" attributes="0"/>
              </Group>
          </Group>
        </DimensionLayout>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="jLabel1">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Segoe UI" size="24" style="0"/>
            </Property>
            <Property name="foreground" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="0" red="0" type="rgb"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Qu&#x1ea3;n L&#xfd; D&#x1ecb;ch V&#x1ee5;"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel2">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="M&#xe3; D&#x1ecb;ch V&#x1ee5;"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel3">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="D&#x1ecb;ch  V&#x1ee5;"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel5">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="&#x110;&#x1a1;n Gi&#xe1; (VN&#x110;)"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JTextField" name="txtmadichvu">
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="txtmadichvuActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JTextField" name="txtdongia">
    </Component>
    <Container class="javax.swing.JScrollPane" name="jScrollPane2">
      <AuxValues>
        <AuxValue name="autoScrollPane" type="java.lang.Boolean" value="true"/>
      </AuxValues>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Component class="javax.swing.JTable" name="tblbangdichvu">
          <Properties>
            <Property name="model" type="javax.swing.table.TableModel" editor="org.netbeans.modules.form.editors2.TableModelEditor">
              <Table columnCount="3" rowCount="4">
                <Column editable="true" title="M&#xe3; D&#x1ecb;ch V&#x1ee5;" type="java.lang.Object"/>
                <Column editable="true" title="T&#xea;n D&#x1ecb;ch V&#x1ee5;" type="java.lang.Object"/>
                <Column editable="true" title="&#x110;&#x1a1;n Gi&#xe1; (VN&#x110;)" type="java.lang.Object"/>
              </Table>
            </Property>
            <Property name="columnModel" type="javax.swing.table.TableColumnModel" editor="org.netbeans.modules.form.editors2.TableColumnModelEditor">
              <TableColumnModel selectionModel="0">
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
                <Column maxWidth="-1" minWidth="-1" prefWidth="-1" resizable="true">
                  <Title/>
                  <Editor/>
                  <Renderer/>
                </Column>
              </TableColumnModel>
            </Property>
            <Property name="tableHeader" type="javax.swing.table.JTableHeader" editor="org.netbeans.modules.form.editors2.JTableHeaderEditor">
              <TableHeader reorderingAllowed="true" resizingAllowed="true"/>
            </Property>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JLabel" name="jLabel4">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="B&#x1ea3;ng D&#x1ecb;ch V&#x1ee5;"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel6">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="M&#xf4; T&#x1ea3; &#x110;&#x1a1;n H&#xe0;ng"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JComboBox" name="cbodichvu">
      <Properties>
        <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
          <StringArray count="4">
            <StringItem index="0" value="Item 1"/>
            <StringItem index="1" value="Item 2"/>
            <StringItem index="2" value="Item 3"/>
            <StringItem index="3" value="Item 4"/>
          </StringArray>
        </Property>
      </Properties>
      <AuxValues>
        <AuxValue name="JavaCodeGenerator_TypeParameters" type="java.lang.String" value="&lt;String&gt;"/>
      </AuxValues>
    </Component>
    <Component class="javax.swing.JButton" name="btnthem">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="Th&#xea;m "/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="btnsua">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="S&#x1eed;a"/>
      </Properties>
      <Events>
        <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="btnsuaActionPerformed"/>
      </Events>
    </Component>
    <Component class="javax.swing.JButton" name="btnxoa">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="X&#xf3;a"/>
      </Properties>
    </Component>
    <Component class="javax.swing.JButton" name="btnlammoi">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Segoe UI" size="16" style="0"/>
        </Property>
        <Property name="text" type="java.lang.String" value="L&#xe0;m M&#x1edb;i"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
